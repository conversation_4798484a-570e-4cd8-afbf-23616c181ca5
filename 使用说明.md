# WGS84坐标系距离计算器

## 项目简介
本项目实现了基于WGS84坐标系的两点间距离计算功能，用于计算给定中心点与多个坐标点之间的距离。

## 功能特性
- 支持WGS84坐标系（EPSG:4326）
- 使用geodesic距离计算（考虑地球椭球体）
- 高精度距离计算，单位为米
- 自动解析坐标数据
- 提供统计信息（平均距离、最大距离、最小距离）

## 文件结构
```
├── README.md                    # 项目需求说明
├── requirements.txt             # Python依赖包
├── distance_calculator.py       # 主程序文件
├── test_distance_calculator.py  # 测试文件
└── 使用说明.md                  # 本文件
```

## 安装依赖
```bash
pip install -r requirements.txt
```

## 运行程序
```bash
python distance_calculator.py
```

## 程序输出示例
```
WGS84坐标系距离计算器
==================================================
中心点坐标: 经度 112.96036064, 纭度 27.28728583
==================================================
共找到 11 个坐标点

计算结果:
--------------------------------------------------------------------------------
点编号      经度                 纬度                 距离(米)        
--------------------------------------------------------------------------------
299      112.960345280000   27.287274820000    1.949565       
316      112.960353760000   27.287280120000    0.929658
...
--------------------------------------------------------------------------------
平均距离: 1.312880 米
最大距离: 2.157514 米
最小距离: 0.165740 米
```

## 运行测试
```bash
python test_distance_calculator.py
```

## 技术说明
- 使用geopy库的geodesic函数进行距离计算
- geodesic距离考虑了地球的椭球体形状，比简单的球面距离更精确
- 坐标系统：WGS84 (EPSG:4326)
- 距离单位：米(m)

## 依赖包说明
- `geopy==2.4.1`: 地理位置计算库，提供高精度的距离计算功能

## 数据格式
程序解析的原始数据格式：
```
{"x":经度,"y":纬度,"spatialReference":{"wkid":4326}}, 点编号, 其他数值
```

其中：
- x: 经度值
- y: 纬度值  
- spatialReference.wkid: 4326表示WGS84坐标系
- 点编号: 用于标识每个坐标点
- 其他数值: 程序会忽略的额外数据
