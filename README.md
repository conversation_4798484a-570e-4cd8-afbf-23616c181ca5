## 坐标系
WGS84坐标系

## 求给定经纬度 两点之间的距离
中心点：Longitude :112.96036064
       Latitude: 27.28728583


需要计算的数据
{"x":112.96034528000007,"y":27.28727482000005,"spatialReference":{"wkid":4326}},299,1.969112589834285
{"x":112.96035376000009,"y":27.287280120000048,"spatialReference":{"wkid":4326}},316,0.9451269563713574
{"x":112.96034953000003,"y":27.287286910000034,"spatialReference":{"wkid":4326}},317,1.17914084163198
{"x":112.96034529000008,"y":27.28729369000007,"spatialReference":{"wkid":4326}},318,1.836160926425703
{"x":112.96036648000006,"y":27.287278640000068,"spatialReference":{"wkid":4326}},332,0.9017516186785555
{"x":112.96036225000012,"y":27.287285420000046,"spatialReference":{"wkid":4326}},333,0.09348259748268388
{"x":112.96035801000005,"y":27.287292210000032,"spatialReference":{"wkid":4326}},334,0.8283957746141887
{"x":112.96037920000003,"y":27.287277150000023,"spatialReference":{"wkid":4326}},349,1.9901029215333474
{"x":112.96037496000008,"y":27.287283940000066,"spatialReference":{"wkid":4326}},350,1.3602444281281658
{"x":112.96037073000002,"y":27.287290720000044,"spatialReference":{"wkid":4326}},351,1.1068522939506955
{"x":112.96037921000004,"y":27.287296020000042,"spatialReference":{"wkid":4326}},369,2.1309469219344415

用,分割上面的数据， 第一个列是 需要计算的点的位置， 第二列是 点的编号，第三列是使用arcgis runtime sdk for android 计算出来的距离
方法是
```kotlin
val distance = GeometryEngine.distanceGeodetic(
                                    centerPoint,
                                    targetPoint,
                                    LinearUnit(LinearUnitId.METERS),
                                    AngularUnit(AngularUnitId.DEGREES),
                                    GeodeticCurveType.GEODESIC
                                ).distance
```

## 需求
现在使用Python， 计算出中心点和这些给定点之间的距离，单位用m表示

添加必要的依赖