#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WGS84坐标系下两点间距离计算器
计算给定中心点与多个坐标点之间的距离（单位：米）
"""

import json
from geopy.distance import geodesic


def parse_coordinate_data():
    """
    解析README.md中的坐标数据
    返回坐标点列表，每个元素包含坐标和点编号
    """
    # 从README.md中提取的原始数据
    raw_data = [
        '{"x":112.96034528000007,"y":27.28727482000005,"spatialReference":{"wkid":4326}}, 299,1.969112589834285',
        '{"x":112.96035376000009,"y":27.287280120000048,"spatialReference":{"wkid":4326}}, 316,0.9451269563713574',
        '{"x":112.96034953000003,"y":27.287286910000034,"spatialReference":{"wkid":4326}}, 317,1.17914084163198',
        '{"x":112.96034529000008,"y":27.28729369000007,"spatialReference":{"wkid":4326}}, 318,1.836160926425703',
        '{"x":112.96036648000006,"y":27.287278640000068,"spatialReference":{"wkid":4326}}, 332,0.9017516186785555',
        '{"x":112.96036225000012,"y":27.287285420000046,"spatialReference":{"wkid":4326}}, 333,0.09348259748268388',
        '{"x":112.96035801000005,"y":27.287292210000032,"spatialReference":{"wkid":4326}}, 334,0.8283957746141887',
        '{"x":112.96037920000003,"y":27.287277150000023,"spatialReference":{"wkid":4326}}, 349,1.9901029215333474',
        '{"x":112.96037496000008,"y":27.287283940000066,"spatialReference":{"wkid":4326}}, 350,1.3602444281281658',
        '{"x":112.96037073000002,"y":27.287290720000044,"spatialReference":{"wkid":4326}}, 351,1.1068522939506955',
        '{"x":112.96037921000004,"y":27.287296020000042,"spatialReference":{"wkid":4326}}, 369,2.1309469219344415'
    ]
    
    coordinates = []
    for line in raw_data:
        # 分割数据：坐标JSON, 其余部分
        parts = line.split(', ')

        if len(parts) >= 2:  # 至少需要2部分：坐标JSON, 其余数据
            coord_json = parts[0]
            remaining_data = parts[1]  # 包含点编号和其他数值

            try:
                # 解析坐标JSON
                coord_data = json.loads(coord_json)
                longitude = coord_data['x']
                latitude = coord_data['y']

                # 从剩余数据中提取点编号（第一个逗号前的部分）
                remaining_parts = remaining_data.split(',')
                if len(remaining_parts) >= 1:
                    point_id = int(remaining_parts[0])

                    coordinates.append({
                        'id': point_id,
                        'longitude': longitude,
                        'latitude': latitude
                    })

            except (json.JSONDecodeError, KeyError, ValueError, IndexError) as e:
                # 静默跳过解析错误的行
                continue
    
    return coordinates


def calculate_distance(center_lat, center_lon, point_lat, point_lon):
    """
    使用geopy库计算两点间的距离（WGS84坐标系）
    
    Args:
        center_lat: 中心点纬度
        center_lon: 中心点经度
        point_lat: 目标点纬度
        point_lon: 目标点经度
    
    Returns:
        距离（米）
    """
    center_point = (center_lat, center_lon)
    target_point = (point_lat, point_lon)
    
    # 使用geodesic距离计算（考虑地球椭球体）
    distance = geodesic(center_point, target_point).meters
    return distance


def main():
    """
    主函数：计算中心点到各个坐标点的距离
    """
    # 中心点坐标（从README.md获取）
    center_longitude = 112.96036064
    center_latitude = 27.28728583
    
    print("WGS84坐标系距离计算器")
    print("=" * 50)
    print(f"中心点坐标: 经度 {center_longitude}, 纬度 {center_latitude}")
    print("=" * 50)
    
    # 解析坐标数据
    coordinates = parse_coordinate_data()
    
    if not coordinates:
        print("未找到有效的坐标数据")
        return
    
    print(f"共找到 {len(coordinates)} 个坐标点")
    print()
    
    # 计算每个点到中心点的距离
    results = []
    for coord in coordinates:
        distance = calculate_distance(
            center_latitude, center_longitude,
            coord['latitude'], coord['longitude']
        )
        
        results.append({
            'id': coord['id'],
            'longitude': coord['longitude'],
            'latitude': coord['latitude'],
            'distance_meters': distance
        })
    
    # 按点编号排序
    results.sort(key=lambda x: x['id'])
    
    # 输出结果
    print("计算结果:")
    print("-" * 80)
    print(f"{'点编号':<8} {'经度':<18} {'纬度':<18} {'距离(米)':<15}")
    print("-" * 80)
    
    for result in results:
        print(f"{result['id']:<8} {result['longitude']:<18.12f} {result['latitude']:<18.12f} {result['distance_meters']:<15.6f}")
    
    print("-" * 80)
    print(f"平均距离: {sum(r['distance_meters'] for r in results) / len(results):.6f} 米")
    print(f"最大距离: {max(r['distance_meters'] for r in results):.6f} 米")
    print(f"最小距离: {min(r['distance_meters'] for r in results):.6f} 米")


if __name__ == "__main__":
    main()
