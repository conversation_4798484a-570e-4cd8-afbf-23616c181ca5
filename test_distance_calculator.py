#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
距离计算器的测试文件
"""

import unittest
from distance_calculator import calculate_distance, parse_coordinate_data


class TestDistanceCalculator(unittest.TestCase):
    
    def test_calculate_distance_same_point(self):
        """测试相同点的距离应该为0"""
        distance = calculate_distance(27.28728583, 112.96036064, 27.28728583, 112.96036064)
        self.assertAlmostEqual(distance, 0, places=2)
    
    def test_calculate_distance_known_points(self):
        """测试已知点之间的距离计算"""
        # 北京到上海的大致距离（约1000公里）
        beijing_lat, beijing_lon = 39.9042, 116.4074
        shanghai_lat, shanghai_lon = 31.2304, 121.4737
        
        distance = calculate_distance(beijing_lat, beijing_lon, shanghai_lat, shanghai_lon)
        
        # 北京到上海的距离大约在1000-1200公里之间
        self.assertGreater(distance, 1000000)  # 大于1000公里
        self.assertLess(distance, 1300000)     # 小于1300公里
    
    def test_parse_coordinate_data(self):
        """测试坐标数据解析功能"""
        coordinates = parse_coordinate_data()
        
        # 应该解析出11个坐标点
        self.assertEqual(len(coordinates), 11)
        
        # 检查第一个点的数据
        first_point = coordinates[0]
        self.assertIn('id', first_point)
        self.assertIn('longitude', first_point)
        self.assertIn('latitude', first_point)
        
        # 检查数据类型
        self.assertIsInstance(first_point['id'], int)
        self.assertIsInstance(first_point['longitude'], float)
        self.assertIsInstance(first_point['latitude'], float)
    
    def test_coordinate_data_values(self):
        """测试解析的坐标数据值是否合理"""
        coordinates = parse_coordinate_data()
        
        for coord in coordinates:
            # 经度应该在112.96左右
            self.assertGreater(coord['longitude'], 112.95)
            self.assertLess(coord['longitude'], 112.97)
            
            # 纬度应该在27.28左右
            self.assertGreater(coord['latitude'], 27.28)
            self.assertLess(coord['latitude'], 27.29)
            
            # 点编号应该是正整数
            self.assertGreater(coord['id'], 0)


if __name__ == '__main__':
    unittest.main()
